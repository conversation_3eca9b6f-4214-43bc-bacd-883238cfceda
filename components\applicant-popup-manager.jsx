"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/lib/auth-context"
import { ApplicantPopup } from "./applicant-popup"
import useStore from "@/lib/store"

export function ApplicantPopupManager() {
  const { user } = useAuth()
  const { incrementApplicantCount } = useStore()
  const [popupQueue, setPopupQueue] = useState([])
  const [currentPopup, setCurrentPopup] = useState(null)
  const [isVisible, setIsVisible] = useState(false)

  // Handle new applicant notification
  const handleNewApplicant = (data) => {
    console.log('ApplicantNotification received with data:', data)

    const applicantData = {
      node_position: data.node_position,
      user_id: data.user_id,
      user_rate: data.user_rate,
      user_leave_count: data.user_leave_count,
      user_reject_count: data.user_reject_count
    }

    console.log('Adding applicant to popup queue:', applicantData)

    // Increment applicant count in store for real-time UI updates
    // Pass the node_id if available, otherwise fallback to node_position
    const nodeIdentifier = data.node_id || data.node_position
    const identifierType = data.node_id ? 'id' : 'position'

    console.log(`Incrementing contracts_count using ${identifierType}: ${nodeIdentifier}`)
    incrementApplicantCount(nodeIdentifier, identifierType)

    setPopupQueue(prev => [...prev, applicantData])
  }

  // Initialize WebSocket connection for applicant notifications
  useEffect(() => {
    if (!user?.id) return

    const setupWebSocket = async () => {
      try {
        // Load Socket.IO if not already loaded
        if (!window.io) {
          const socketScript = document.createElement('script')
          socketScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/socket.io/2.5.0/socket.io.js'
          document.head.appendChild(socketScript)

          await new Promise((resolve, reject) => {
            socketScript.onload = resolve
            socketScript.onerror = reject
          })
        }

        // Load Laravel Echo if not already loaded
        if (!window.Echo) {
          const echoScript = document.createElement('script')
          echoScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/laravel-echo/1.15.3/echo.iife.js'
          document.head.appendChild(echoScript)

          await new Promise((resolve, reject) => {
            echoScript.onload = resolve
            echoScript.onerror = reject
          })
        }

        // Initialize Echo
        const echo = new window.Echo({
          broadcaster: 'socket.io',
          host: 'http://127.0.0.1:6001',
          forceNew: true,
          transports: ['websocket', 'polling']
        })

        // Listen to the applicant notification channel
        const applicantChannel = echo.channel(`laravel-database-new.applicant.for.user-${user.id}`)
        console.log('Listening to applicant popup channel:', `laravel-database-new.applicant.for.user-${user.id}`)

        // Listen for ApplicantNotification events
        applicantChannel
          .listen('.ApplicantNotification', (data) => {
            console.log('ApplicantNotification received for popup:', data)
            handleNewApplicant(data)
          })
          .listen('ApplicantNotification', (data) => {
            console.log('ApplicantNotification received for popup:', data)
            handleNewApplicant(data)
          })

        console.log('WebSocket connection established for applicant popups')

        // Cleanup function
        return () => {
          if (echo) {
            echo.disconnect()
          }
        }

      } catch (error) {
        console.error('WebSocket setup failed for applicant popups:', error)
      }
    }

    const cleanup = setupWebSocket()
    return () => {
      if (cleanup && typeof cleanup.then === 'function') {
        cleanup.then(cleanupFn => cleanupFn && cleanupFn())
      }
    }
  }, [user?.id])

  // Add test functions for development
  useEffect(() => {
    window.testApplicantNotification = handleNewApplicant

    // Test function with node_id
    window.testApplicantNotificationWithNodeId = (nodeId) => {
      handleNewApplicant({
        node_id: nodeId,
        node_position: "Test Position",
        user_id: 999,
        user_rate: 8,
        user_leave_count: 1,
        user_reject_count: 0
      })
    }

    // Test function with only node_position (fallback)
    window.testApplicantNotificationWithPosition = (nodePosition) => {
      handleNewApplicant({
        node_position: nodePosition,
        user_id: 999,
        user_rate: 8,
        user_leave_count: 1,
        user_reject_count: 0
      })
    }

    return () => {
      delete window.testApplicantNotification
      delete window.testApplicantNotificationWithNodeId
      delete window.testApplicantNotificationWithPosition
    }
  }, [])



  // Process popup queue
  useEffect(() => {
    if (popupQueue.length > 0 && !isVisible) {
      const nextApplicant = popupQueue[0]
      console.log('Showing next applicant popup:', nextApplicant)
      
      setCurrentPopup(nextApplicant)
      setIsVisible(true)
      setPopupQueue(prev => prev.slice(1))
    }
  }, [popupQueue, isVisible])

  const handlePopupClose = () => {
    console.log('Closing applicant popup')
    setIsVisible(false)
    setCurrentPopup(null)
  }

  return (
    <ApplicantPopup
      applicantData={currentPopup}
      isVisible={isVisible}
      onClose={handlePopupClose}
    />
  )
}
